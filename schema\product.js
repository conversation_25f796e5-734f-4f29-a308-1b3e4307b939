const joi = require('joi');

// 获取商品列表的验证规则
const product_list_schema = {
  query: {
    keyword: joi.string().allow('').optional(),
    page: joi.number().integer().min(1).default(1),
    size: joi.number().integer().min(1).max(100).default(10)
  }
};

// 获取商品详情的验证规则
const product_detail_schema = {
  query: {
    id: joi.number().integer().required()
  }
};

// 新增商品的验证规则
const product_add_schema = {
  body: {
    code: joi.string().required(),
    name: joi.string().required(),    
    model: joi.string().required(),
    standard: joi.string().required(),
    price: joi.number().min(0).required(),
    remark: joi.string().allow('').optional(),
    items: joi.array().required(),
    status: joi.string().valid('1', '0').default('1')
  }
};

// 更新商品的验证规则
const product_update_schema = {
  body: {
    id: joi.number().integer().required(),
    code: joi.string().required(),
    name: joi.string().required(),    
    model: joi.string().required(),
    standard: joi.string().required(),
    price: joi.number().min(0).required(),
    remark: joi.string().allow('').optional(),
    items: joi.array().required(),
    status: joi.string().valid('1', '0').default('1')
  }
};

// 删除商品的验证规则
const product_delete_schema = {
  body: {
    id: joi.number().integer().required()
  }
};

module.exports = {
  product_list_schema,
  product_detail_schema,
  product_add_schema,
  product_update_schema,
  product_delete_schema
};
