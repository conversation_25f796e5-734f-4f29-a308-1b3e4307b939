const db = require('../db/index')
const log = require('./log')

const getCustomerList = (req, res) => {
  try {
    const user = req.user;

    // 获取查询参数
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 10;
    const keyword = req.query.keyword || '';

    console.log("查询参数:", { page, size, keyword });

    // 构建SQL查询
    let extSql = "";
    let params = [];

    if (keyword && keyword.trim() !== '') {
      extSql = "AND (`code` LIKE ? OR `name` LIKE ? OR `phone` LIKE ?) ";
      const searchTerm = `%${keyword}%`;
      params = [searchTerm, searchTerm, searchTerm];
    }

    // 计算分页
    const offset = (page - 1) * size;

    // 查询总数
    const countSql = "SELECT COUNT(*) as total FROM customers WHERE delFlag = 0  " + extSql;

    db.query(countSql, params, (countErr, countResults) => {
      if (countErr) {
        console.error("查询总数出错:", countErr);
        return res.output(countErr, 500);
      }

      const total = countResults[0].total;

      // 查询数据
      const dataSql = "SELECT	* FROM customers "
        + "WHERE delFlag = 0 "
        + extSql + " LIMIT ?, ?";
      const dataParams = [...params, offset, size];

      db.query(dataSql, dataParams, (err, results) => {
        if (err) {
          console.error("查询数据出错:", err);
          return res.output(err, 500);
        }     

        // 返回结果
        res.output(null, 200, {
          total: total,
          page: page,
          size: size,
          list: results
        });
      });
    });
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

const getCustomerDetail = (req, res) => {
  try {
    const user = req.user;
    const id = req.query.id;
    const sql = "select * from customers where id=?";
    db.query(sql, id, (err, results) => {
      if (err) return res.output(err)
      if (results.length !== 1) return res.output(new Error("获取客户信息失败！"), 400)
      // 移除密码字段
      const customerInfo = { ...results[0] };     
      res.output(null, 200, customerInfo);
    })
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

const addCustomer = (req, res) => {
  try {
    const user = req.user;
    const { name, code, companyName, address, contact, phone, status, remark } = req.body;
    const sqlStr = "insert into customers set ?";
    db.query(sqlStr, { name, code, companyName, address, contact, phone, status, remark }, (err, results) => {
      if (err) return res.output(err);
      if (results.affectedRows !== 1) return res.output(new Error("添加客户失败！"), 400);
      res.output(null, 200, { id: results.insertId, message: "添加客户成功" }, true);
    });
    log.add("sysLog", "添加客户:" + name, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

const updateCustomer = (req, res) => {
  try {
    const user = req.user;
    const { id, name, code, companyName, address, contact, phone, status, remark } = req.body;
    const sqlStr = 'update customers set name=?, code=?, companyName=?, address=?, contact=?, phone=?, status=?, remark=? where id =?'
    db.query(sqlStr, [name, code, companyName, address, contact, phone, status, remark, id], (err, results) => {
      if (err) return res.output(err)
      if (results.affectedRows !== 1) return res.output(new Error('更新客户信息失败！'), 400)
      res.output(null, 200, { id: id, name: name, code: code, status: status }, true)
    })
    log.add("sysLog", "更新客户信息:" + name, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

const deleteCustomer = (req, res) => {
  try {
    const user = req.user;
    const id = req.body.id;
    const sqlStr = "update customers set delFlag=1 where id=?";
    db.query(sqlStr, id, (err, results) => {
      if (err) return res.output(err);
      if (results.affectedRows !== 1) return res.output(new Error("删除客户失败！"), 400);
      res.output(null, 200, { message: "删除客户成功" }, true);
    });
    log.add("sysLog", "删除客户:" + id, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

module.exports = {
  getCustomerList,
  getCustomerDetail,
  addCustomer,
  updateCustomer,
  deleteCustomer
};
