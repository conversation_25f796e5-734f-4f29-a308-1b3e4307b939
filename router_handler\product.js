const db = require('../db/index')
const log = require('./log')
const { IMAGE_TYPES, createUploader, handleFileUpload, deleteImage } = require('../utils/imageManager');

// 获取商品列表
const getProductList = (req, res) => {
  try {
    const user = req.user;

    // 获取查询参数
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 10;
    const keyword = req.query.keyword || '';

    console.log("查询参数:", { page, size, keyword });

    // 构建SQL查询
    let extSql = "";
    let params = [];

    if (keyword && keyword.trim() !== '') {
      extSql = "AND (`code` LIKE ? OR `name` LIKE ? OR `model` LIKE ? OR 'standard' LIKE ? ) ";
      const searchTerm = `%${keyword}%`;
      params = [searchTerm, searchTerm, searchTerm, searchTerm];
    }

    // 计算分页
    const offset = (page - 1) * size;

    // 查询总数
    const countSql = "SELECT COUNT(*) as total FROM products WHERE delFlag = 0  " + extSql;

    db.query(countSql, params, (countErr, countResults) => {
      if (countErr) {
        console.error("查询总数出错:", countErr);
        return res.output(countErr, 500);
      }

      const total = countResults[0].total;

      // 查询数据
      const dataSql = "SELECT	* FROM products "
        + "WHERE delFlag = 0 "
        + extSql + " LIMIT ?, ?";
      const dataParams = [...params, offset, size];

      db.query(dataSql, dataParams, (err, results) => {
        if (err) {
          console.error("查询数据出错:", err);
          return res.output(err, 500);
        }

        // 返回结果
        res.output(null, 200, {
          total: total,
          page: page,
          size: size,
          list: results
        });
      });
    });
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取商品详情
const getProductDetail = (req, res) => {
  try {
    const { id } = req.query;
    const sql = 'select * from products where id= ?'

    db.query(sql, id, (err, results) => {
      if (err) return res.output(err)
      if (results.length !== 1) return res.output(new Error("获取商品信息失败！"), 400)
      const product = { ...results[0] };
      if (product.id) {
        const sqlStr = "SELECT pc.number,pc.status,cs.* FROM `product_composition` pc "
          + "LEFT JOIN components cs ON pc.componentId = cs.id "
          + "WHERE	pc.productId = ?"
        db.query(sqlStr, product.id, (err, itemResults) => {
          if (err) return res.output(err)
          res.output(null, 200, { product: product, componentList: itemResults }, true);
        })
      } else {
        res.output(null, 200, { product: product, componentList: {} }, true);
      }
    })
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 新增商品
const addProduct = (req, res) => {
  const uploader = createUploader(IMAGE_TYPES.PRODUCT, 10);

  uploader.array('images', 10)(req, res, async function (err) {
    if (err) {
      return res.output(err, 400, null, false);
    }

    try {
      const { code, name, model, standard, price, remark, status, items } = req.body;
      const files = req.files || [];
      const uploadUserId = req.user ? req.user.id : null;
      const uploadUserName = req.user ? req.user.username : null;

      // 解析 items 数据（如果是字符串则解析为数组）
      let itemsArray = [];
      if (typeof items === 'string') {
        try {
          itemsArray = JSON.parse(items);
        } catch (parseErr) {
          return res.output(new Error('items 格式错误'), 400, null, false);
        }
      } else if (Array.isArray(items)) {
        itemsArray = items;
      }

      // 开始数据库事务
      db.beginTransaction((transErr) => {
        if (transErr) {
          return res.output(transErr, 500, null, false);
        }

        // 1. 插入产品主表数据
        const productSql = 'INSERT INTO products (code, name, model, standard, price, remark, status, delFlag) VALUES (?, ?, ?, ?, ?, ?, ?, 0)';
        const productParams = [code, name, model, standard, price, remark || '', status || 1];

        db.query(productSql, productParams, async (productErr, productResults) => {
          if (productErr) {
            return db.rollback(() => {
              res.output(productErr, 500, null, false);
            });
          }

          const productId = productResults.insertId;

          // 2. 插入产品构成数据
          if (itemsArray && itemsArray.length > 0) {
            const compositionSql = 'INSERT INTO product_composition (productId, componentId, number, status) VALUES ?';
            const compositionValues = itemsArray.map(item => [
              productId,
              item.componentId,
              item.number || 1,
              item.status || 1
            ]);

            db.query(compositionSql, [compositionValues], async (compositionErr) => {
              if (compositionErr) {
                return db.rollback(() => {
                  res.output(compositionErr, 500, null, false);
                });
              }

              // 3. 处理图片上传
              let savedImages = [];
              if (files.length > 0) {
                try {
                  savedImages = await handleFileUpload(files, IMAGE_TYPES.PRODUCT, {
                    businessType: IMAGE_TYPES.PRODUCT,
                    businessId: productId.toString(),
                    uploadUserId: uploadUserId,
                    uploadUserName: uploadUserName
                  });
                } catch (uploadErr) {
                  return db.rollback(() => {
                    res.output(uploadErr, 500, null, false);
                  });
                }
              }

              // 提交事务
              db.commit((commitErr) => {
                if (commitErr) {
                  return db.rollback(() => {
                    res.output(commitErr, 500, null, false);
                  });
                }

                // 记录日志
                log.add("sysLog", `新增产品: ${name}`, uploadUserName || 'system');

                res.output(null, 200, {
                  id: productId,
                  images: savedImages,
                  message: '产品添加成功'
                }, true);
              });
            });
          } else {
            // 没有构成数据，直接处理图片上传
            let savedImages = [];
            if (files.length > 0) {
              try {
                savedImages = await handleFileUpload(files, IMAGE_TYPES.PRODUCT, {
                  businessType: IMAGE_TYPES.PRODUCT,
                  businessId: productId.toString(),
                  uploadUserId: uploadUserId,
                  uploadUserName: uploadUserName
                });
              } catch (uploadErr) {
                return db.rollback(() => {
                  res.output(uploadErr, 500, null, false);
                });
              }
            }

            // 提交事务
            db.commit((commitErr) => {
              if (commitErr) {
                return db.rollback(() => {
                  res.output(commitErr, 500, null, false);
                });
              }

              // 记录日志
              log.add("sysLog", `新增产品: ${name}`, uploadUserName || 'system');

              res.output(null, 200, {
                id: productId,
                images: savedImages,
                message: '产品添加成功'
              }, true);
            });
          }
        });
      });
    } catch (error) {
      res.output(error, 500, null, false);
    }
  });
};

// 更新商品
const updateProduct = (req, res) => {
  const uploader = createUploader(IMAGE_TYPES.PRODUCT, 10);

  uploader.array('images', 10)(req, res, async function (err) {
    if (err) {
      return res.output(err, 400, null, false);
    }

    try {
      const { id, code, name, model, standard, price, remark, status, items, deleteImageIds } = req.body;
      const files = req.files || [];
      const uploadUserId = req.user ? req.user.id : null;
      const uploadUserName = req.user ? req.user.username : null;

      // 验证产品ID
      if (!id) {
        return res.output(new Error('产品ID不能为空'), 400, null, false);
      }

      // 解析 items 数据（如果是字符串则解析为数组）
      let itemsArray = [];
      if (typeof items === 'string') {
        try {
          itemsArray = JSON.parse(items);
        } catch (parseErr) {
          return res.output(new Error('items 格式错误'), 400, null, false);
        }
      } else if (Array.isArray(items)) {
        itemsArray = items;
      }

      // 开始数据库事务
      db.beginTransaction((transErr) => {
        if (transErr) {
          return res.output(transErr, 500, null, false);
        }

        // 1. 更新产品主表数据
        const updateProductSql = 'UPDATE products SET code = ?, name = ?, model = ?, standard = ?, price = ?, remark = ?, status = ? WHERE id = ? AND delFlag = 0';
        const updateProductParams = [code, name, model, standard, price, remark || '', status || 1, id];

        db.query(updateProductSql, updateProductParams, (updateErr, updateResults) => {
          if (updateErr) {
            return db.rollback(() => {
              res.output(updateErr, 500, null, false);
            });
          }

          if (updateResults.affectedRows === 0) {
            return db.rollback(() => {
              res.output(new Error('产品不存在或已被删除'), 404, null, false);
            });
          }

          // 2. 删除原有的产品构成数据
          const deleteCompositionSql = 'DELETE FROM product_composition WHERE productId = ?';

          db.query(deleteCompositionSql, [id], (deleteErr) => {
            if (deleteErr) {
              return db.rollback(() => {
                res.output(deleteErr, 500, null, false);
              });
            }

            // 3. 插入新的产品构成数据
            if (itemsArray && itemsArray.length > 0) {
              const compositionSql = 'INSERT INTO product_composition (productId, componentId, number, status) VALUES ?';
              const compositionValues = itemsArray.map(item => [
                id,
                item.componentId,
                item.number || 1,
                item.status || 1
              ]);

              db.query(compositionSql, [compositionValues], (compositionErr) => {
                if (compositionErr) {
                  return db.rollback(() => {
                    res.output(compositionErr, 500, null, false);
                  });
                }

                handleImageOperations();
              });
            } else {
              handleImageOperations();
            }
          });
        });

        // 处理图片操作的函数
        async function handleImageOperations() {
          try {
            // 4. 处理删除的图片
            if (deleteImageIds && Array.isArray(deleteImageIds)) {
              for (const imageId of deleteImageIds) {
                try {
                  await deleteImage(imageId);
                } catch (error) {
                  console.error('删除商品图片失败:', imageId, error);
                }
              }
            }

            // 5. 处理新上传的图片
            let savedImages = [];
            if (files.length > 0) {
              savedImages = await handleFileUpload(files, IMAGE_TYPES.PRODUCT, {
                businessType: IMAGE_TYPES.PRODUCT,
                businessId: id.toString(),
                uploadUserId: uploadUserId,
                uploadUserName: uploadUserName
              });
            }

            // 提交事务
            db.commit((commitErr) => {
              if (commitErr) {
                return db.rollback(() => {
                  res.output(commitErr, 500, null, false);
                });
              }

              // 记录日志
              log.add("sysLog", `更新产品: ${name}`, uploadUserName || 'system');

              res.output(null, 200, {
                id: id,
                newImages: savedImages,
                deletedImageIds: deleteImageIds || [],
                message: '产品更新成功'
              }, true);
            });
          } catch (error) {
            db.rollback(() => {
              res.output(error, 500, null, false);
            });
          }
        }
      });
    } catch (error) {
      res.output(error, 500, null, false);
    }
  });
};

// 删除商品
const deleteProduct = (req, res) => {
  try {
    const { id } = req.body;
    const user = req.user;

    // 验证产品ID
    if (!id) {
      return res.output(new Error('产品ID不能为空'), 400, null, false);
    }

    // 开始数据库事务
    db.beginTransaction((transErr) => {
      if (transErr) {
        return res.output(transErr, 500, null, false);
      }

      // 1. 软删除产品主表数据（设置 delFlag = 1）
      const deleteProductSql = 'UPDATE products SET delFlag = 1 WHERE id = ? AND delFlag = 0';

      db.query(deleteProductSql, [id], (deleteErr, deleteResults) => {
        if (deleteErr) {
          return db.rollback(() => {
            res.output(deleteErr, 500, null, false);
          });
        }

        if (deleteResults.affectedRows === 0) {
          return db.rollback(() => {
            res.output(new Error('产品不存在或已被删除'), 404, null, false);
          });
        }

        // 2. 删除产品构成数据
        const deleteCompositionSql = 'DELETE FROM product_composition WHERE productId = ?';

        db.query(deleteCompositionSql, [id], (compositionErr) => {
          if (compositionErr) {
            return db.rollback(() => {
              res.output(compositionErr, 500, null, false);
            });
          }

          // 提交事务
          db.commit((commitErr) => {
            if (commitErr) {
              return db.rollback(() => {
                res.output(commitErr, 500, null, false);
              });
            }

            // 记录日志
            log.add("sysLog", `删除产品: ID=${id}`, user ? user.username : 'system');

            res.output(null, 200, {
              message: '产品删除成功'
            }, true);
          });
        });
      });
    });
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

module.exports = {
  getProductList,
  getProductDetail,
  addProduct,
  updateProduct,
  deleteProduct
};
