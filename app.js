const express = require('express');
const app = express();
const joi = require('joi');
const multer = require('multer');
const path = require('path');

// 导入并配置cors中间件， 解决跨域
const cors = require('cors')
app.use(cors())

// Serve static files from the web directory
app.use(express.static('web'));

// 将图片的文件夹转为静态文件
app.use(express.static('images'))
app.use(express.json())
// 配置解析表单数据的中间件  内置中间件，只能解析application/x-www-form-urlencoded格式的数据
app.use(express.urlencoded({ extended: false }))

// 配置 multer 中间件
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/')
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname))
  }
})

// 创建multer实例
const upload = multer({ storage: storage })

// 不使用全局multer中间件，而是在每个路由中单独配置
// 这样可以避免multer配置冲突
console.log("Using route-specific multer configuration");

// 添加 output 中间件
app.use((req, res, next) => {
  res.output = function (err, status = 200, data = null, success = null) {
    const response = {
      code: status,
      message: err instanceof Error ? err.message : (err || '操作成功'),
      data,
      success: success !== null ? success : (status >= 200 && status < 300)
    }
    res.status(status).json(response);
  };
  next();
});

const jwt = require("express-jwt");
const config = require('./config')
// 定义中间件，需要哪个密钥解析，.unless指定哪些接口不需要进行token身份认证
// 表示所有以/api开头的路径都不需要JWT认证
try {
  app.use(
    jwt({ secret: config.jwtSecretKey, algorithms: ["HS256"] }).unless({
      path: [
        /^\/auth/,
        '/api',
        '/api-docs',
        /^\/api\/module\/list/,    
        /^\/api\/image\/uploadImage/,
        /^\/api\/image\/uploadMultipleImages/,
        /^\/api\/image\/list/,       
        /^\/images\//,  // 允许直接访问图片文件，无需身份验证
        '/api/department',
        '/api/position',
        '/api/job',
        '/departments',
        '/positions',
        '/posts',
        '/permissions',
        '/processes'
      ],
    })
  );
} catch (error) {
  console.error("JWT middleware error:", error);
  // 如果JWT中间件初始化失败，使用一个简单的替代中间件
  app.use((req, res, next) => {
    // 跳过认证，直接进入下一个中间件
    next();
  });
}

// 导入API文档生成器
const { apiRoutes, generateDetailedApiDocs } = require('./utils/apiDocGenerator');

//注册和登录
const authRouter = require("./router/auth");
app.use("/auth", authRouter);

// 用户管理
const userRouter = require("./router/user");
app.use("/api/user", userRouter);

// 部门管理
const departmentRouter = require("./router/department");
app.use("/api/department", departmentRouter);

// 职位管理
const positionRouter = require("./router/position");
app.use("/api/position", positionRouter);

// 岗位管理
const jobRouter = require("./router/job");
app.use("/api/post", jobRouter);

// 流程管理
const processRouter = require("./router/process");
app.use("/api/process", processRouter);

// 权限管理
const processPostMappingRouter = require("./router/processPostMapping");
app.use("/api/processPostMapping", processPostMappingRouter);

// 权限管理
const permissionRouter = require("./router/permission");
app.use("/api/permission", permissionRouter);

// 客户管理
const customerRouter = require("./router/customer");
app.use("/api/customer", customerRouter);

// 待办事项管理
const todoRouter = require("./router/todo");
app.use("/api/todo", todoRouter);

// 商品管理
const productRouter = require("./router/product");
app.use("/api/product", productRouter);

// 部件管理
const componentRouter = require("./router/component");
app.use("/api/component", componentRouter);

// 订单管理
const orderRouter = require("./router/order");
app.use("/api/order", orderRouter);


// 跟进管理
const followRouter = require("./router/follow");
app.use("/api/follow", followRouter);

// 图片管理
const imageRouter = require("./router/image");
app.use("/api/image", imageRouter);

// 静态图片文件服务 - 无需身份验证
app.use('/images', express.static('images'));

// Android专用路由 - 不带/api前缀
app.use("/departments", departmentRouter);
app.use("/positions", positionRouter);
app.use("/posts", jobRouter);
app.use("/permissions", permissionRouter);
app.use("/processes", processRouter);

// 错误处理中间件
app.use((err, req, res, next) => {
  // 确保res.output存在，如果不存在则创建一个
  if (!res.output) {
    res.output = function (err, status = 200, data = null, success = null) {
      const response = {
        code: status,
        message: err instanceof Error ? err.message : (err || '操作成功'),
        data,
        success: success !== null ? success : (status >= 200 && status < 300)
      }
      res.status(status).json(response);
    };
  }

  // 处理不同类型的错误
  if (err instanceof joi.ValidationError) {
    return res.output(err, 400, null, false);
  }
  if (err instanceof multer.MulterError) {
    return res.output(err, 400, null, false);
  }

  // 处理其他类型的错误
  const statusCode = err.statusCode || 500;
  res.output(err, statusCode, null, false);
});

//后台作业
const work = require('./work/work')
work.startWork();

app.get('/api', (req, res) => {
  res.json(apiRoutes);
});

// API文档页面
app.get('/api-docs', (req, res) => {
  const apiSpec = require('./config/apiSpec');
  const html = generateDetailedApiDocs(apiRoutes, apiSpec);
  res.send(html);
});

// 启动服务器
app.listen(3007, () => {
  console.log('Server running at http://127.0.0.1:3007');
});