// 测试产品功能的示例代码
// 这个文件用于测试 addProduct 和 updateProduct 功能

const testData = {
  // 新增产品测试数据
  addProductData: {
    code: "P001",
    name: "测试产品1",
    model: "M001",
    standard: "标准规格",
    price: 100.50,
    remark: "这是一个测试产品",
    status: 1,
    items: [
      {
        componentId: 1,
        number: 2,
        status: 1
      },
      {
        componentId: 2,
        number: 1,
        status: 1
      }
    ]
  },

  // 更新产品测试数据
  updateProductData: {
    id: 1,
    code: "P001-UPDATED",
    name: "更新后的测试产品1",
    model: "M001-V2",
    standard: "更新后的标准规格",
    price: 120.00,
    remark: "这是一个更新后的测试产品",
    status: 1,
    items: [
      {
        componentId: 1,
        number: 3,
        status: 1
      },
      {
        componentId: 3,
        number: 2,
        status: 1
      }
    ],
    deleteImageIds: [] // 要删除的图片ID数组
  }
};

// 测试用的 API 请求示例
const apiExamples = {
  // 新增产品 API 调用示例
  addProduct: `
POST /api/product/add
Content-Type: multipart/form-data

FormData:
- code: "P001"
- name: "测试产品1"
- model: "M001"
- standard: "标准规格"
- price: 100.50
- remark: "这是一个测试产品"
- status: 1
- items: '[{"componentId":1,"number":2,"status":1},{"componentId":2,"number":1,"status":1}]'
- images: [文件1, 文件2] (可选)
`,

  // 更新产品 API 调用示例
  updateProduct: `
POST /api/product/update
Content-Type: multipart/form-data

FormData:
- id: 1
- code: "P001-UPDATED"
- name: "更新后的测试产品1"
- model: "M001-V2"
- standard: "更新后的标准规格"
- price: 120.00
- remark: "这是一个更新后的测试产品"
- status: 1
- items: '[{"componentId":1,"number":3,"status":1},{"componentId":3,"number":2,"status":1}]'
- deleteImageIds: '["image-id-1", "image-id-2"]' (可选)
- images: [新文件1, 新文件2] (可选)
`,

  // 删除产品 API 调用示例
  deleteProduct: `
POST /api/product/delete
Content-Type: application/json

{
  "id": 1
}
`
};

// 数据库表结构说明
const tableStructures = {
  products: `
CREATE TABLE products (
  id int(11) NOT NULL AUTO_INCREMENT,
  code varchar(255) COMMENT '编号',
  name varchar(255) COMMENT '名称',
  model varchar(255) COMMENT '型号',
  standard varchar(255) COMMENT '规格',
  remark varchar(255) COMMENT '说明',
  price decimal(10, 2) COMMENT '单价',
  status int(11) NOT NULL DEFAULT 1 COMMENT '状态（1在售，-1停售，0预售）',
  delFlag int(11) NOT NULL DEFAULT 0 COMMENT '删除标志',
  PRIMARY KEY (id)
);
`,

  product_composition: `
CREATE TABLE product_composition (
  id int(11) NOT NULL AUTO_INCREMENT,
  productId int(11) NOT NULL COMMENT '产品Id',
  componentId int(11) NOT NULL COMMENT '组件Id',
  number int(11) NOT NULL DEFAULT 1 COMMENT '数量',
  status int(11) NOT NULL DEFAULT 1 COMMENT '状态（1正常，0停用）',
  createTime datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updateTime datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  INDEX idx_product_id(productId),
  INDEX idx_component_id(componentId)
);
`
};

console.log('产品功能测试数据和API示例已准备完成');
console.log('请参考 testData 对象中的数据结构进行测试');
console.log('请参考 apiExamples 对象中的API调用示例');
console.log('请参考 tableStructures 对象中的数据库表结构');

module.exports = {
  testData,
  apiExamples,
  tableStructures
};
