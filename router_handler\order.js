const db = require('../db/index')
const log = require('./log')
const { IMAGE_TYPES, createUploader, handleFileUpload, deleteImage } = require('../utils/imageManager');

const addOrder = (req, res) => {
    const uploader = createUploader(IMAGE_TYPES.PRODUCT, 10);

    uploader.array('images', 10)(req, res, async function (err) {
        if (err) {
            return res.output(err, 400, null, false);
        }

        try {
            const { name, code, category, price, stock, status, description, specifications } = req.body;
            const files = req.files || [];
            const uploadUserId = req.user ? req.user.id : null;
            const uploadUserName = req.user ? req.user.username : null;

            // 模拟新增操作
            const newId = Date.now().toString();

            let savedImages = [];
            if (files.length > 0) {
                savedImages = await handleFileUpload(files, IMAGE_TYPES.PRODUCT, {
                    businessType: 'product',
                    businessId: newId,
                    uploadUserId: uploadUserId,
                    uploadUserName: uploadUserName
                });
            }

            res.output(null, 200, {
                id: newId,
                images: savedImages
            }, true);
        } catch (error) {
            res.output(error, 500, null, false);
        }
    });
};

module.exports = {
    addOrder
};
